<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端面试评估系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .interview-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 30px;
        }
        
        .interview-info div {
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .content {
            padding: 30px;
        }
        
        /* 导航栏样式 */
        .navigation {
            position: sticky;
            top: 0;
            background: white;
            padding: 15px 20px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border: 2px solid #4facfe;
            z-index: 1000;
        }
        
        .nav-sections {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-bottom: 15px;
            justify-content: center;
        }
        
        .nav-btn {
            padding: 8px 16px;
            background: #f8f9ff;
            border: 2px solid #e1e8ed;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #666;
            position: relative;
            overflow: hidden;
        }
        
        .nav-btn:hover, .nav-btn.active {
            background: #4facfe;
            color: white;
            border-color: #4facfe;
            transform: translateY(-2px);
        }
        
        .nav-btn::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: #4facfe;
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }
        
        .nav-btn.active::after,
        .nav-btn:hover::after {
            width: 100%;
        }
        
        .controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .candidate-info {
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .candidate-input {
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 1em;
            min-width: 150px;
            transition: all 0.3s ease;
        }
        
        .candidate-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }
        
        .score-display {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            padding: 12px 24px;
            background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
            border-radius: 25px;
            min-width: 140px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }
        
        .progress-container {
            margin-top: 15px;
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9em;
            color: #666;
        }
        
        .progress-bar {
            width: 100%;
            height: 10px;
            background: #e1e8ed;
            border-radius: 5px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.5s ease;
            border-radius: 5px;
        }
        
        /* 章节样式 */
        .section {
            margin-bottom: 40px;
            background: #f8f9ff;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 5px solid #4facfe;
            scroll-margin-top: 120px;
        }
        
        .section-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e1e8ed;
        }
        
        .section-subtitle {
            color: #4facfe;
            margin: 25px 0 15px 0;
            font-size: 1.3em;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .section-subtitle::before {
            content: "▶";
            font-size: 0.8em;
        }
        
        /* 问题样式 */
        .question {
            margin-bottom: 25px;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }
        
        .question:hover {
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }
        
        .question-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .question-number {
            background: #4facfe;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9em;
            font-weight: bold;
            flex-shrink: 0;
            transition: transform 0.2s ease;
        }
        
        .question:hover .question-number {
            transform: scale(1.1);
        }
        
        .question-text {
            font-size: 1.1em;
            font-weight: 600;
            color: #2c3e50;
            flex: 1;
            line-height: 1.5;
        }
        
        .answer-reference {
            font-size: 0.95em;
            color: #666;
            background: linear-gradient(135deg, #f0f7ff 0%, #e8f4fd 100%);
            padding: 18px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
            position: relative;
        }
        
        .answer-reference::before {
            content: "💡";
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.2em;
        }
        
        .answer-reference strong {
            color: #2c3e50;
            display: block;
            margin-bottom: 8px;
        }
        
        .input-group {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        
        .answer-input {
            flex: 1;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 1em;
            min-height: 100px;
            resize: vertical;
            transition: all 0.3s ease;
            font-family: inherit;
        }
        
        .answer-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 4px rgba(79, 172, 254, 0.1);
        }
        
        .score-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            align-items: center;
            min-width: 80px;
        }
        
        .score-input {
            width: 70px;
            height: 50px;
            padding: 8px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .score-input:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
            transform: scale(1.05);
        }
        
        .score-input.invalid {
            border-color: #ff6b6b;
            background-color: #ffe0e0;
        }
        
        .score-label {
            font-size: 0.85em;
            color: #666;
            text-align: center;
            font-weight: 600;
        }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .content {
                padding: 20px;
            }
            
            .section {
                padding: 20px;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding: 5px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .interview-info {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }
            
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .candidate-info {
                flex-direction: column;
                align-items: stretch;
            }
            
            .candidate-info label {
                font-weight: 600;
                margin-bottom: 5px;
            }
            
            .action-buttons {
                justify-content: center;
            }
            
            .nav-sections {
                justify-content: center;
            }
            
            .input-group {
                flex-direction: column;
                gap: 15px;
            }
            
            .score-group {
                flex-direction: row;
                justify-content: center;
                min-width: auto;
            }
            
            .question {
                padding: 20px;
            }
            
            .section-title {
                font-size: 1.5em;
            }
        }
        
        @media (max-width: 480px) {
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .content {
                padding: 15px;
            }
            
            .section {
                padding: 15px;
            }
            
            .question {
                padding: 15px;
            }
            
            .nav-btn {
                font-size: 0.8em;
                padding: 6px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>前端面试评估系统</h1>
            <div class="interview-info">
                <div><strong>👨‍💼 面试官：</strong>左浩</div>
                <div><strong>⏱️ 面试时长：</strong>65分钟</div>
                <div><strong>💻 面试方式：</strong>远程视频面试</div>
            </div>
        </div>
        
        <div class="content">
            <div class="navigation">
                <div class="nav-sections">
                    <a href="#section-opening" class="nav-btn">开场</a>
                    <a href="#section-intro" class="nav-btn">自我介绍</a>
                    <a href="#section-basic" class="nav-btn">基础技术</a>
                    <a href="#section-framework" class="nav-btn">框架应用</a>
                    <a href="#section-project" class="nav-btn">项目经验</a>
                    <a href="#section-vision" class="nav-btn">技术视野</a>
                    <a href="#section-summary" class="nav-btn">面试总结</a>
                </div>
                <div class="controls-row">
                    <div class="candidate-info">
                        <label>候选人姓名：</label>
                        <input type="text" id="candidateName" class="candidate-input" placeholder="请输入候选人姓名" required>
                        <label>面试日期：</label>
                        <input type="date" id="interviewDate" class="candidate-input" required>
                    </div>
                    <div class="score-display">
                        平均分：<span id="averageScore">0.0</span>/5.0
                    </div>
                    <div class="action-buttons">
                        <button onclick="autoSave()" class="btn btn-secondary">💾 保存进度</button>
                        <button onclick="exportToExcel()" class="btn btn-primary">📊 导出Excel</button>
                    </div>
                </div>
                <div class="progress-container">
                    <div class="progress-info">
                        <span>完成进度</span>
                        <span id="progressText">0/30 题</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>

            <!-- 开场部分 -->
            <div class="section" id="section-opening">
                <h2 class="section-title">🎯 开场部分</h2>
                <div class="question">
                    <div class="question-header">
                        <div class="question-number">1</div>
                        <div class="question-text">面试官开场白</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考内容：</strong>"您好，同学！欢迎来参加今天的面试，我是今天的面试官，我叫左浩，目前负责我们这个部门的一面面试工作。首先感谢您抽出宝贵时间过来交流，不用紧张，我们今天更像是一次轻松的技术探讨。如果准备好，那我们开始吧？"
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人反应和回应..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第一部分：自我介绍 -->
            <div class="section" id="section-intro">
                <h2 class="section-title">👋 第一部分：自我介绍（5分钟）</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">2</div>
                        <div class="question-text">请简单介绍一下自己的技术背景和项目经验</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>技术栈的广度和深度、项目经验的相关性、表达的逻辑性和清晰度、对技术的理解程度
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">3</div>
                        <div class="question-text">为什么选择前端开发这个方向？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>对前端开发的理解和认知、职业规划的清晰度、学习动机和热情
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二部分：基础技术理解 -->
            <div class="section" id="section-basic">
                <h2 class="section-title">🔧 第二部分：基础技术理解（25分钟）</h2>

                <h3 class="section-subtitle">JavaScript基础概念</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">4</div>
                        <div class="question-text">能解释一下JavaScript中的作用域和作用域链吗？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>作用域是变量的可访问性和生命周期的规则。JavaScript有全局作用域、函数作用域和块级作用域。作用域链是指当访问一个变量时，JavaScript引擎会先在当前作用域查找，如果没有找到，就向上一级作用域查找，直到全局作用域，这个查找过程形成的链式结构就是作用域链。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">5</div>
                        <div class="question-text">var、let、const的区别是什么？什么时候选择使用哪个？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>var有函数作用域，存在变量提升，可以重复声明；let有块级作用域，不存在变量提升，不可重复声明；const有块级作用域，声明时必须赋值，声明后不可修改（对象内容可修改）。使用建议：优先使用const，需要修改时使用let，避免使用var。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">6</div>
                        <div class="question-text">什么是闭包？你在实际开发中遇到过闭包的应用场景吗？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>闭包是指函数可以访问其外部作用域的变量，即使外部函数已经执行完毕。应用场景包括：模块化、数据私有化、函数柯里化、回调函数等。比如事件处理器、定时器回调、模块模式等。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">7</div>
                        <div class="question-text">JavaScript的数据类型有哪些？如何进行类型检测？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>基本类型：number、string、boolean、null、undefined、symbol、bigint；引用类型：object（包括数组、函数等）。类型检测方法：typeof（基本类型）、instanceof（引用类型）、Object.prototype.toString.call()（精确检测）、Array.isArray()（数组检测）。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">异步编程理解</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">8</div>
                        <div class="question-text">Promise和async/await的区别是什么？你更倾向于使用哪种方式？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>Promise是异步编程的一种解决方案，用链式调用处理异步操作；async/await是Promise的语法糖，让异步代码看起来像同步代码，更易读易维护。async/await在错误处理和调试方面更友好，但Promise在某些复杂场景下更灵活。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">9</div>
                        <div class="question-text">在前端开发中，什么情况下会用到异步编程？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>AJAX请求、文件上传下载、定时器、事件处理、动画、数据库操作、文件读写、网络通信等。任何可能阻塞用户界面或需要等待外部资源的操作都需要异步处理。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">10</div>
                        <div class="question-text">什么是事件循环（Event Loop）？微任务和宏任务的区别？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>事件循环是JavaScript处理异步操作的机制。宏任务包括setTimeout、setInterval、I/O操作等；微任务包括Promise.then、queueMicrotask等。执行顺序：同步代码 → 微任务队列 → 宏任务队列，每个宏任务执行完后都会清空微任务队列。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">DOM操作与事件</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">11</div>
                        <div class="question-text">事件冒泡和事件捕获的区别？如何阻止事件冒泡？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>事件冒泡是事件从目标元素向上传播到根元素；事件捕获是从根元素向下传播到目标元素。可以通过event.stopPropagation()阻止事件冒泡，event.preventDefault()阻止默认行为。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">12</div>
                        <div class="question-text">如何优化大量DOM操作的性能？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>批量操作DOM、使用DocumentFragment、虚拟滚动、减少重排重绘、使用事件委托、缓存DOM查询结果、使用requestAnimationFrame等。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">13</div>
                        <div class="question-text">什么是事件委托？有什么优势？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>事件委托是利用事件冒泡原理，将事件监听器添加到父元素上，通过判断event.target来处理子元素事件。优势：减少内存占用、动态元素无需重新绑定、提高性能。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">HTML/CSS基础</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">14</div>
                        <div class="question-text">说说盒模型的理解？border-box和content-box的区别？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>盒模型包括content、padding、border、margin。content-box（标准盒模型）：width/height只包含content；border-box（怪异盒模型）：width/height包含content、padding、border。border-box更符合直觉，便于布局计算。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">15</div>
                        <div class="question-text">flex布局和grid布局分别适用于什么场景？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>Flex布局适用于一维布局（行或列），如导航栏、工具栏、卡片排列等；Grid布局适用于二维布局（行和列），如网页整体布局、复杂的网格系统、卡片网格等。Flex更简单，Grid更强大。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">16</div>
                        <div class="question-text">如何实现元素的水平垂直居中？你知道几种方法？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>1.Flex布局：justify-content: center; align-items: center; 2.Grid布局：place-items: center; 3.绝对定位+transform：position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); 4.绝对定位+margin：已知宽高时使用负margin; 5.table-cell方法等。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三部分：框架应用 -->
            <div class="section" id="section-framework">
                <h2 class="section-title">⚛️ 第三部分：框架应用（15分钟）</h2>

                <h3 class="section-subtitle">React/Vue框架理解</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">17</div>
                        <div class="question-text">你主要使用哪个前端框架？能说说它的核心特性和优势吗？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>对框架核心概念的理解、能够说出框架的优势和适用场景、有实际使用经验
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">18</div>
                        <div class="question-text">React的虚拟DOM是什么？它解决了什么问题？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>虚拟DOM是React在内存中维护的虚拟DOM树，通过diff算法比较新旧虚拟DOM的差异，只更新变化的部分。解决了频繁操作真实DOM性能差的问题，提供了更好的开发体验和性能优化。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">19</div>
                        <div class="question-text">React Hooks有哪些？useState和useEffect的使用场景？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>常用Hooks：useState（状态管理）、useEffect（副作用处理）、useContext（上下文）、useReducer（复杂状态）、useMemo（性能优化）等。useState用于组件状态管理，useEffect用于处理副作用如API调用、订阅、清理等。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">20</div>
                        <div class="question-text">组件间通信有哪些方式？什么时候使用状态管理库？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>父子组件：props和回调；兄弟组件：状态提升或Context；跨层级：Context、状态管理库。当组件层级深、状态复杂、多组件共享状态时考虑使用Redux、Zustand等状态管理库。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">性能优化</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">21</div>
                        <div class="question-text">React/Vue项目中如何进行性能优化？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>参考答案：</strong>代码分割（lazy loading）、组件缓存（React.memo、useMemo）、虚拟滚动、图片懒加载、减少不必要的渲染、使用生产环境构建、CDN加速、gzip压缩等。
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第四部分：项目经验 -->
            <div class="section" id="section-project">
                <h2 class="section-title">💼 第四部分：项目经验（15分钟）</h2>

                <h3 class="section-subtitle">项目介绍</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">22</div>
                        <div class="question-text">请介绍一个你最有成就感的项目，你在其中承担了什么角色？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>项目的复杂度和技术含量、个人贡献和角色、解决问题的能力、项目成果和影响
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">23</div>
                        <div class="question-text">在项目开发过程中遇到过什么技术难点？是如何解决的？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>问题的复杂程度、解决思路的合理性、学习和研究能力、举一反三的能力
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">24</div>
                        <div class="question-text">项目的技术架构是怎样的？为什么选择这样的技术栈？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>对技术架构的理解、技术选型的合理性、对项目需求的分析能力
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">团队协作</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">25</div>
                        <div class="question-text">在项目中你是如何与后端同事协作的？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>协作能力、沟通技巧、接口设计理解、跨部门合作经验
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">26</div>
                        <div class="question-text">代码review的流程是怎样的？你认为代码review有什么价值？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>代码质量意识、团队协作精神、持续改进思维、对最佳实践的理解
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第五部分：技术视野与发展 -->
            <div class="section" id="section-vision">
                <h2 class="section-title">🔮 第五部分：技术视野与发展（5分钟）</h2>

                <h3 class="section-subtitle">技术选型思考</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">27</div>
                        <div class="question-text">如果让你选择一个前端框架开始新项目，你会考虑哪些因素？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>技术选型的系统性思考、对不同框架的理解、项目需求分析能力、长远规划考虑
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <h3 class="section-subtitle">学习与成长</h3>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">28</div>
                        <div class="question-text">你平时是如何学习新的前端技术的？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>学习能力、自我驱动力、学习方法的有效性、持续学习的习惯
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">29</div>
                        <div class="question-text">对我们团队或者这个岗位有什么想了解的吗？</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评分要点：</strong>求职动机、对公司和岗位的了解程度、问题的质量、职业规划的清晰度
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="记录候选人的回答..."></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">分数<br>(1-5)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 面试总结 -->
            <div class="section" id="section-summary">
                <h2 class="section-title">📝 面试总结</h2>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">30</div>
                        <div class="question-text">整体评价和建议</div>
                    </div>
                    <div class="answer-reference">
                        <strong>评价维度：</strong>技术基础、项目经验、学习能力、沟通表达、团队协作、发展潜力
                    </div>
                    <div class="input-group">
                        <textarea class="answer-input" placeholder="请写下对候选人的整体评价和建议..." style="min-height: 120px;"></textarea>
                        <div class="score-group">
                            <input type="number" min="1" max="5" step="0.1" class="score-input" onchange="calculateAverage()" oninput="validateScore(this)">
                            <div class="score-label">总体<br>评分</div>
                        </div>
                    </div>
                </div>

                <div class="question">
                    <div class="question-header">
                        <div class="question-number">-</div>
                        <div class="question-text">是否推荐进入下一轮面试？</div>
                    </div>
                    <div class="input-group">
                        <select class="answer-input" style="min-height: auto; padding: 12px;" onchange="updateRecommendation(this)">
                            <option value="">请选择</option>
                            <option value="strong-yes">🌟 强烈推荐</option>
                            <option value="yes">✅ 推荐</option>
                            <option value="maybe">🤔 待定</option>
                            <option value="no">❌ 不推荐</option>
                        </select>
                        <div class="score-group">
                            <div class="score-label">推荐<br>程度</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 设置当前日期
        document.getElementById('interviewDate').value = new Date().toISOString().split('T')[0];

        // 分数验证
        function validateScore(input) {
            const value = parseFloat(input.value);
            const min = parseFloat(input.min);
            const max = parseFloat(input.max);

            if (isNaN(value) || value < min || value > max) {
                input.classList.add('invalid');
            } else {
                input.classList.remove('invalid');
            }
        }

        // 计算平均分
        function calculateAverage() {
            const scoreInputs = document.querySelectorAll('.score-input');
            let total = 0;
            let count = 0;

            scoreInputs.forEach(input => {
                const value = parseFloat(input.value);
                if (!isNaN(value) && value >= 1 && value <= 5) {
                    total += value;
                    count++;
                }
            });

            const average = count > 0 ? (total / count) : 0;
            document.getElementById('averageScore').textContent = average.toFixed(1);
            document.getElementById('progressText').textContent = `${count}/30 题`;

            // 更新进度条
            const progress = (count / 30) * 100;
            document.getElementById('progressFill').style.width = progress + '%';

            // 根据分数改变颜色
            const scoreDisplay = document.querySelector('.score-display');
            if (average >= 4.5) {
                scoreDisplay.style.background = 'linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)';
            } else if (average >= 3.5) {
                scoreDisplay.style.background = 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)';
            } else if (average > 0) {
                scoreDisplay.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
            } else {
                scoreDisplay.style.background = 'linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)';
            }
        }

        // 更新推荐状态
        function updateRecommendation(select) {
            const value = select.value;
            const colors = {
                'strong-yes': 'linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%)',
                'yes': 'linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%)',
                'maybe': 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                'no': 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
            };

            if (colors[value]) {
                select.style.background = colors[value];
                select.style.color = '#333';
                select.style.fontWeight = 'bold';
            }
        }

        // 导航功能
        function initNavigation() {
            const navBtns = document.querySelectorAll('.nav-btn');
            const sections = document.querySelectorAll('.section');

            // 点击导航按钮
            navBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = btn.getAttribute('href').substring(1);
                    const targetSection = document.getElementById(targetId);

                    if (targetSection) {
                        targetSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // 更新活跃状态
                        navBtns.forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                    }
                });
            });

            // 滚动时更新导航状态
            window.addEventListener('scroll', () => {
                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 150;
                    if (window.pageYOffset >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });

                navBtns.forEach(btn => {
                    btn.classList.remove('active');
                    if (btn.getAttribute('href') === `#${current}`) {
                        btn.classList.add('active');
                    }
                });
            });
        }

        // 自动保存功能
        function autoSave() {
            try {
                const data = {
                    candidateName: document.getElementById('candidateName').value,
                    interviewDate: document.getElementById('interviewDate').value,
                    timestamp: new Date().toISOString(),
                    answers: {},
                    scores: {}
                };

                document.querySelectorAll('.answer-input').forEach((input, index) => {
                    data.answers[index] = input.value;
                });

                document.querySelectorAll('.score-input').forEach((input, index) => {
                    data.scores[index] = input.value;
                });

                // 保存到localStorage
                localStorage.setItem('interviewData', JSON.stringify(data));

                // 显示保存成功提示
                showNotification('数据已保存到本地', 'success');
            } catch (e) {
                showNotification('保存失败：' + e.message, 'error');
            }
        }

        // 加载保存的数据
        function loadSavedData() {
            try {
                const savedData = localStorage.getItem('interviewData');
                if (savedData) {
                    const data = JSON.parse(savedData);

                    // 恢复基本信息
                    if (data.candidateName) {
                        document.getElementById('candidateName').value = data.candidateName;
                    }
                    if (data.interviewDate) {
                        document.getElementById('interviewDate').value = data.interviewDate;
                    }

                    // 恢复答案
                    document.querySelectorAll('.answer-input').forEach((input, index) => {
                        if (data.answers && data.answers[index]) {
                            input.value = data.answers[index];
                        }
                    });

                    // 恢复分数
                    document.querySelectorAll('.score-input').forEach((input, index) => {
                        if (data.scores && data.scores[index]) {
                            input.value = data.scores[index];
                        }
                    });

                    calculateAverage();
                    showNotification('已恢复上次保存的数据', 'info');
                }
            } catch (e) {
                console.warn('加载保存数据失败:', e);
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: bold;
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
                max-width: 300px;
            `;

            const colors = {
                success: '#4CAF50',
                error: '#f44336',
                info: '#2196F3',
                warning: '#ff9800'
            };

            notification.style.backgroundColor = colors[type] || colors.info;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease-out';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 导出Excel功能
        function exportToExcel() {
            const candidateName = document.getElementById('candidateName').value || '未填写';
            const interviewDate = document.getElementById('interviewDate').value || '未填写';
            const averageScore = document.getElementById('averageScore').textContent;

            if (!candidateName || candidateName === '未填写') {
                showNotification('请先填写候选人姓名', 'warning');
                return;
            }

            // 收集所有问题和答案
            const questions = document.querySelectorAll('.question');
            const data = [];

            // 添加基本信息
            data.push(['前端面试评估报告']);
            data.push(['']);
            data.push(['候选人姓名', candidateName]);
            data.push(['面试日期', interviewDate]);
            data.push(['面试官', '左浩']);
            data.push(['面试时长', '65分钟']);
            data.push(['面试方式', '远程视频面试']);
            data.push(['平均分', averageScore + '/5.0']);
            data.push(['']); // 空行

            // 添加问题标题行
            data.push(['序号', '问题', '参考答案/评分要点', '候选人回答', '得分']);

            questions.forEach((question, index) => {
                const questionNumber = question.querySelector('.question-number')?.textContent || (index + 1);
                const questionText = question.querySelector('.question-text').textContent.trim();
                const answerRef = question.querySelector('.answer-reference')?.textContent
                    .replace('参考答案：', '').replace('评分要点：', '')
                    .replace('参考内容：', '').replace('评价维度：', '').trim() || '';
                const candidateAnswer = question.querySelector('.answer-input').value || '未回答';
                const scoreInput = question.querySelector('.score-input');
                const score = scoreInput ? (scoreInput.value || '未评分') : '无需评分';

                data.push([questionNumber, questionText, answerRef, candidateAnswer, score]);
            });

            try {
                // 创建工作簿
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(data);

                // 设置列宽
                ws['!cols'] = [
                    { width: 8 },   // 序号
                    { width: 60 },  // 问题
                    { width: 80 },  // 参考答案
                    { width: 80 },  // 候选人回答
                    { width: 12 }   // 得分
                ];

                // 添加工作表到工作簿
                XLSX.utils.book_append_sheet(wb, ws, '面试记录');

                // 导出文件
                const fileName = `前端面试记录_${candidateName}_${interviewDate}.xlsx`;
                XLSX.writeFile(wb, fileName);

                showNotification('Excel文件导出成功', 'success');
            } catch (error) {
                showNotification('导出失败：' + error.message, 'error');
            }
        }

        // 键盘快捷键
        function initKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl+S 保存
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    autoSave();
                }

                // Ctrl+E 导出
                if (e.ctrlKey && e.key === 'e') {
                    e.preventDefault();
                    exportToExcel();
                }

                // Esc 清除焦点
                if (e.key === 'Escape') {
                    document.activeElement.blur();
                }
            });
        }

        // 自动保存（定时）
        function startAutoSave() {
            setInterval(() => {
                if (document.getElementById('candidateName').value.trim()) {
                    autoSave();
                }
            }, 30000); // 每30秒自动保存一次
        }

        // 监听输入变化
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('score-input')) {
                validateScore(e.target);
                calculateAverage();
            }

            // 实时保存（防抖）
            clearTimeout(window.saveTimeout);
            window.saveTimeout = setTimeout(() => {
                if (document.getElementById('candidateName').value.trim()) {
                    autoSave();
                }
            }, 2000);
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化各种功能
            initNavigation();
            initKeyboardShortcuts();
            loadSavedData();
            startAutoSave();
            calculateAverage();

            // 添加CSS动画
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }

                @keyframes slideOut {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);

            // 显示欢迎信息
            setTimeout(() => {
                showNotification('面试评估系统已就绪，快捷键：Ctrl+S保存，Ctrl+E导出', 'info');
            }, 1000);
        });

        // 页面卸载前保存数据
        window.addEventListener('beforeunload', function(e) {
            if (document.getElementById('candidateName').value.trim()) {
                autoSave();
            }
        });
    </script>
</body>
</html>
